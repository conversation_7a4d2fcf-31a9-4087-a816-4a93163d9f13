body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.search-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.search-form {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-btn {
  padding: 12px 25px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  min-width: 120px;
}

.search-btn:hover {
  background: #5a6fd8;
}

.search-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab {
  padding: 10px 20px;
  background: #f8f9fa;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.tab.active {
  background: #667eea;
  color: white;
}

.tab:hover {
  background: #e9ecef;
}

.tab.active:hover {
  background: #5a6fd8;
}

.weather-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.weather-title {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.weather-icon {
  width: 60px;
  height: 60px;
}

.weather-main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.weather-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.weather-value {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.weather-label {
  color: #666;
  font-size: 0.9rem;
}

.flight-conditions {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.flight-status {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.status-excellent {
  background: #28a745;
}

.status-fair {
  background: #ffc107;
}

.status-poor {
  background: #dc3545;
}

.status-text {
  font-size: 1.2rem;
  font-weight: bold;
}

.warnings {
  margin-top: 15px;
}

.warning-item {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
  color: #856404;
}

.error-message {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 15px;
  color: #721c24;
  margin: 20px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.forecast-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.forecast-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
}

.forecast-time {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

.forecast-temp {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin: 10px 0;
}

.forecast-desc {
  font-size: 0.8rem;
  color: #666;
}
