<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象API集成演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .search-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .search-btn {
            padding: 12px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            min-width: 120px;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .result-card.show {
            display: block;
        }
        
        .weather-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .weather-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .weather-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .weather-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            color: #721c24;
            margin: 20px 0;
        }
        
        .flight-status {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        
        .status-excellent { background: #28a745; }
        .status-fair { background: #ffc107; }
        .status-poor { background: #dc3545; }
        
        .warnings {
            margin-top: 15px;
        }
        
        .warning-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌤️ 气象API集成演示</h1>
            <p>集成气象数据的无人机租赁系统</p>
        </div>

        <div class="search-section">
            <div class="search-form">
                <input type="text" id="cityInput" class="search-input" placeholder="请输入城市名称（如：北京、上海、广州）" value="北京">
                <button class="search-btn" onclick="searchWeather()">查询天气</button>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="switchTab('current')">当前天气</button>
                <button class="tab" onclick="switchTab('forecast')">天气预报</button>
                <button class="tab" onclick="switchTab('flight')">飞行条件</button>
            </div>
        </div>

        <div id="loading" class="loading" style="display: none;">
            🔄 正在获取数据...
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <!-- 当前天气 -->
        <div id="currentWeather" class="result-card show">
            <h2>📍 当前天气</h2>
            <div id="currentWeatherContent"></div>
        </div>

        <!-- 天气预报 -->
        <div id="forecastWeather" class="result-card">
            <h2>📅 天气预报</h2>
            <div id="forecastWeatherContent"></div>
        </div>

        <!-- 飞行条件 -->
        <div id="flightConditions" class="result-card">
            <h2>🚁 飞行条件评估</h2>
            <div id="flightConditionsContent"></div>
        </div>
    </div>

    <script>
        let currentTab = 'current';
        const API_BASE = 'http://localhost:5000/api';

        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 隐藏所有结果卡片
            document.querySelectorAll('.result-card').forEach(card => card.classList.remove('show'));
            
            // 显示对应的卡片
            if (tab === 'current') {
                document.getElementById('currentWeather').classList.add('show');
            } else if (tab === 'forecast') {
                document.getElementById('forecastWeather').classList.add('show');
            } else if (tab === 'flight') {
                document.getElementById('flightConditions').classList.add('show');
            }
        }

        async function searchWeather() {
            const city = document.getElementById('cityInput').value.trim();
            if (!city) return;

            showLoading(true);
            hideError();

            try {
                if (currentTab === 'current') {
                    await getCurrentWeather(city);
                } else if (currentTab === 'forecast') {
                    await getForecast(city);
                } else if (currentTab === 'flight') {
                    await getFlightConditions(city);
                }
            } catch (error) {
                showError('获取数据失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        async function getCurrentWeather(city) {
            const response = await fetch(`${API_BASE}/weather/current/${encodeURIComponent(city)}`);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message);
            }

            const weather = data.data;
            document.getElementById('currentWeatherContent').innerHTML = `
                <div class="weather-grid">
                    <div class="weather-item">
                        <div class="weather-value">${weather.temperature}°C</div>
                        <div class="weather-label">温度</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${weather.humidity}%</div>
                        <div class="weather-label">湿度</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${weather.windSpeed} m/s</div>
                        <div class="weather-label">风速</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${weather.visibility} km</div>
                        <div class="weather-label">能见度</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${weather.pressure} hPa</div>
                        <div class="weather-label">气压</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${weather.description}</div>
                        <div class="weather-label">天气状况</div>
                    </div>
                </div>
                <p><strong>城市:</strong> ${weather.city}, ${weather.country}</p>
            `;
        }

        async function getForecast(city) {
            const response = await fetch(`${API_BASE}/weather/forecast/${encodeURIComponent(city)}`);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message);
            }

            const forecast = data.data;
            const forecastHtml = forecast.forecast.slice(0, 8).map(item => `
                <div class="weather-item">
                    <div class="weather-label">${new Date(item.datetime).toLocaleDateString('zh-CN', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit'
                    })}</div>
                    <div class="weather-value">${item.temperature}°C</div>
                    <div class="weather-label">${item.description}</div>
                    <div class="weather-label">💨 ${item.windSpeed} m/s</div>
                </div>
            `).join('');

            document.getElementById('forecastWeatherContent').innerHTML = `
                <p><strong>城市:</strong> ${forecast.city}</p>
                <div class="weather-grid">${forecastHtml}</div>
            `;
        }

        async function getFlightConditions(city) {
            const response = await fetch(`${API_BASE}/drone/flight-conditions/${encodeURIComponent(city)}`);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message);
            }

            const flight = data.data;
            const warningsHtml = flight.warnings.length > 0 ? `
                <div class="warnings">
                    <h3>⚠️ 注意事项:</h3>
                    ${flight.warnings.map(warning => `<div class="warning-item">${warning}</div>`).join('')}
                </div>
            ` : '';

            document.getElementById('flightConditionsContent').innerHTML = `
                <div class="flight-status">
                    <div class="status-indicator status-${flight.flightSuitability}"></div>
                    <div><strong>飞行适宜性:</strong> ${flight.recommendation}</div>
                </div>
                <div class="weather-grid">
                    <div class="weather-item">
                        <div class="weather-value">${flight.conditions.windSpeed} m/s</div>
                        <div class="weather-label">风速</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${flight.conditions.visibility} km</div>
                        <div class="weather-label">能见度</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${flight.conditions.temperature}°C</div>
                        <div class="weather-label">温度</div>
                    </div>
                    <div class="weather-item">
                        <div class="weather-value">${flight.conditions.humidity}%</div>
                        <div class="weather-label">湿度</div>
                    </div>
                </div>
                <p><strong>城市:</strong> ${flight.city}</p>
                ${warningsHtml}
            `;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = '❌ ' + message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        // 页面加载时自动查询北京天气
        window.onload = function() {
            searchWeather();
        };

        // 支持回车键搜索
        document.getElementById('cityInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchWeather();
            }
        });
    </script>
</body>
</html>
