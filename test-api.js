const axios = require('axios');

// 测试API端点
const BASE_URL = 'http://localhost:5000';

async function testAPI() {
  console.log('🧪 开始测试气象API...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 健康检查通过:', healthResponse.data.message);
    console.log('');

    // 测试当前天气
    console.log('2. 测试当前天气API...');
    const weatherResponse = await axios.get(`${BASE_URL}/api/weather/current/北京`);
    if (weatherResponse.data.success) {
      const weather = weatherResponse.data.data;
      console.log('✅ 当前天气获取成功:');
      console.log(`   城市: ${weather.city}`);
      console.log(`   温度: ${weather.temperature}°C`);
      console.log(`   天气: ${weather.description}`);
      console.log(`   风速: ${weather.windSpeed} m/s`);
      console.log(`   湿度: ${weather.humidity}%`);
    }
    console.log('');

    // 测试天气预报
    console.log('3. 测试天气预报API...');
    const forecastResponse = await axios.get(`${BASE_URL}/api/weather/forecast/上海`);
    if (forecastResponse.data.success) {
      const forecast = forecastResponse.data.data;
      console.log('✅ 天气预报获取成功:');
      console.log(`   城市: ${forecast.city}`);
      console.log(`   预报条数: ${forecast.forecast.length}`);
      console.log(`   首条预报: ${forecast.forecast[0].datetime} - ${forecast.forecast[0].temperature}°C`);
    }
    console.log('');

    // 测试飞行条件
    console.log('4. 测试飞行条件评估API...');
    const flightResponse = await axios.get(`${BASE_URL}/api/drone/flight-conditions/广州`);
    if (flightResponse.data.success) {
      const flight = flightResponse.data.data;
      console.log('✅ 飞行条件评估成功:');
      console.log(`   城市: ${flight.city}`);
      console.log(`   飞行适宜性: ${flight.flightSuitability}`);
      console.log(`   建议: ${flight.recommendation}`);
      console.log(`   风速: ${flight.conditions.windSpeed} m/s`);
      console.log(`   能见度: ${flight.conditions.visibility} km`);
      if (flight.warnings.length > 0) {
        console.log(`   警告: ${flight.warnings.join(', ')}`);
      }
    }
    console.log('');

    console.log('🎉 所有API测试完成！');

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    console.log('\n💡 请确保:');
    console.log('   1. 后端服务器正在运行 (npm start)');
    console.log('   2. .env 文件中配置了正确的 WEATHER_API_KEY');
    console.log('   3. 网络连接正常');
  }
}

// 运行测试
testAPI();
