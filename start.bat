@echo off
echo 🚀 启动气象API演示系统...
echo.

echo 📦 检查依赖...
if not exist node_modules (
    echo 安装后端依赖...
    npm install
)

if not exist client\node_modules (
    echo 安装前端依赖...
    cd client
    npm install
    cd ..
)

echo.
echo 🔧 检查环境配置...
if not exist .env (
    echo 创建环境配置文件...
    copy .env.example .env
    echo.
    echo ⚠️  请编辑 .env 文件，添加你的 OpenWeatherMap API 密钥
    echo    获取地址: https://openweathermap.org/api
    echo.
    pause
)

echo.
echo 🌐 启动后端服务器...
start "后端服务器" cmd /k "npm start"

echo.
echo ⏳ 等待后端启动...
timeout /t 3 /nobreak > nul

echo.
echo 🎨 启动前端应用...
start "前端应用" cmd /k "npm run client"

echo.
echo ✅ 系统启动完成！
echo.
echo 📱 前端地址: http://localhost:3000
echo 🔌 后端API: http://localhost:5000
echo.
echo 按任意键退出...
pause > nul
