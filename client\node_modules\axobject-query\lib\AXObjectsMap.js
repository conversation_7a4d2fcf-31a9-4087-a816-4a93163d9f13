"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _iterationDecorator = _interopRequireDefault(require("./util/iterationDecorator"));
var _AbbrRole = _interopRequireDefault(require("./etc/objects/AbbrRole"));
var _AlertDialogRole = _interopRequireDefault(require("./etc/objects/AlertDialogRole"));
var _AlertRole = _interopRequireDefault(require("./etc/objects/AlertRole"));
var _AnnotationRole = _interopRequireDefault(require("./etc/objects/AnnotationRole"));
var _ApplicationRole = _interopRequireDefault(require("./etc/objects/ApplicationRole"));
var _ArticleRole = _interopRequireDefault(require("./etc/objects/ArticleRole"));
var _AudioRole = _interopRequireDefault(require("./etc/objects/AudioRole"));
var _BannerRole = _interopRequireDefault(require("./etc/objects/BannerRole"));
var _BlockquoteRole = _interopRequireDefault(require("./etc/objects/BlockquoteRole"));
var _BusyIndicatorRole = _interopRequireDefault(require("./etc/objects/BusyIndicatorRole"));
var _ButtonRole = _interopRequireDefault(require("./etc/objects/ButtonRole"));
var _CanvasRole = _interopRequireDefault(require("./etc/objects/CanvasRole"));
var _CaptionRole = _interopRequireDefault(require("./etc/objects/CaptionRole"));
var _CellRole = _interopRequireDefault(require("./etc/objects/CellRole"));
var _CheckBoxRole = _interopRequireDefault(require("./etc/objects/CheckBoxRole"));
var _ColorWellRole = _interopRequireDefault(require("./etc/objects/ColorWellRole"));
var _ColumnHeaderRole = _interopRequireDefault(require("./etc/objects/ColumnHeaderRole"));
var _ColumnRole = _interopRequireDefault(require("./etc/objects/ColumnRole"));
var _ComboBoxRole = _interopRequireDefault(require("./etc/objects/ComboBoxRole"));
var _ComplementaryRole = _interopRequireDefault(require("./etc/objects/ComplementaryRole"));
var _ContentInfoRole = _interopRequireDefault(require("./etc/objects/ContentInfoRole"));
var _DateRole = _interopRequireDefault(require("./etc/objects/DateRole"));
var _DateTimeRole = _interopRequireDefault(require("./etc/objects/DateTimeRole"));
var _DefinitionRole = _interopRequireDefault(require("./etc/objects/DefinitionRole"));
var _DescriptionListDetailRole = _interopRequireDefault(require("./etc/objects/DescriptionListDetailRole"));
var _DescriptionListRole = _interopRequireDefault(require("./etc/objects/DescriptionListRole"));
var _DescriptionListTermRole = _interopRequireDefault(require("./etc/objects/DescriptionListTermRole"));
var _DetailsRole = _interopRequireDefault(require("./etc/objects/DetailsRole"));
var _DialogRole = _interopRequireDefault(require("./etc/objects/DialogRole"));
var _DirectoryRole = _interopRequireDefault(require("./etc/objects/DirectoryRole"));
var _DisclosureTriangleRole = _interopRequireDefault(require("./etc/objects/DisclosureTriangleRole"));
var _DivRole = _interopRequireDefault(require("./etc/objects/DivRole"));
var _DocumentRole = _interopRequireDefault(require("./etc/objects/DocumentRole"));
var _EmbeddedObjectRole = _interopRequireDefault(require("./etc/objects/EmbeddedObjectRole"));
var _FeedRole = _interopRequireDefault(require("./etc/objects/FeedRole"));
var _FigcaptionRole = _interopRequireDefault(require("./etc/objects/FigcaptionRole"));
var _FigureRole = _interopRequireDefault(require("./etc/objects/FigureRole"));
var _FooterRole = _interopRequireDefault(require("./etc/objects/FooterRole"));
var _FormRole = _interopRequireDefault(require("./etc/objects/FormRole"));
var _GridRole = _interopRequireDefault(require("./etc/objects/GridRole"));
var _GroupRole = _interopRequireDefault(require("./etc/objects/GroupRole"));
var _HeadingRole = _interopRequireDefault(require("./etc/objects/HeadingRole"));
var _IframePresentationalRole = _interopRequireDefault(require("./etc/objects/IframePresentationalRole"));
var _IframeRole = _interopRequireDefault(require("./etc/objects/IframeRole"));
var _IgnoredRole = _interopRequireDefault(require("./etc/objects/IgnoredRole"));
var _ImageMapLinkRole = _interopRequireDefault(require("./etc/objects/ImageMapLinkRole"));
var _ImageMapRole = _interopRequireDefault(require("./etc/objects/ImageMapRole"));
var _ImageRole = _interopRequireDefault(require("./etc/objects/ImageRole"));
var _InlineTextBoxRole = _interopRequireDefault(require("./etc/objects/InlineTextBoxRole"));
var _InputTimeRole = _interopRequireDefault(require("./etc/objects/InputTimeRole"));
var _LabelRole = _interopRequireDefault(require("./etc/objects/LabelRole"));
var _LegendRole = _interopRequireDefault(require("./etc/objects/LegendRole"));
var _LineBreakRole = _interopRequireDefault(require("./etc/objects/LineBreakRole"));
var _LinkRole = _interopRequireDefault(require("./etc/objects/LinkRole"));
var _ListBoxOptionRole = _interopRequireDefault(require("./etc/objects/ListBoxOptionRole"));
var _ListBoxRole = _interopRequireDefault(require("./etc/objects/ListBoxRole"));
var _ListItemRole = _interopRequireDefault(require("./etc/objects/ListItemRole"));
var _ListMarkerRole = _interopRequireDefault(require("./etc/objects/ListMarkerRole"));
var _ListRole = _interopRequireDefault(require("./etc/objects/ListRole"));
var _LogRole = _interopRequireDefault(require("./etc/objects/LogRole"));
var _MainRole = _interopRequireDefault(require("./etc/objects/MainRole"));
var _MarkRole = _interopRequireDefault(require("./etc/objects/MarkRole"));
var _MarqueeRole = _interopRequireDefault(require("./etc/objects/MarqueeRole"));
var _MathRole = _interopRequireDefault(require("./etc/objects/MathRole"));
var _MenuBarRole = _interopRequireDefault(require("./etc/objects/MenuBarRole"));
var _MenuButtonRole = _interopRequireDefault(require("./etc/objects/MenuButtonRole"));
var _MenuItemRole = _interopRequireDefault(require("./etc/objects/MenuItemRole"));
var _MenuItemCheckBoxRole = _interopRequireDefault(require("./etc/objects/MenuItemCheckBoxRole"));
var _MenuItemRadioRole = _interopRequireDefault(require("./etc/objects/MenuItemRadioRole"));
var _MenuListOptionRole = _interopRequireDefault(require("./etc/objects/MenuListOptionRole"));
var _MenuListPopupRole = _interopRequireDefault(require("./etc/objects/MenuListPopupRole"));
var _MenuRole = _interopRequireDefault(require("./etc/objects/MenuRole"));
var _MeterRole = _interopRequireDefault(require("./etc/objects/MeterRole"));
var _NavigationRole = _interopRequireDefault(require("./etc/objects/NavigationRole"));
var _NoneRole = _interopRequireDefault(require("./etc/objects/NoneRole"));
var _NoteRole = _interopRequireDefault(require("./etc/objects/NoteRole"));
var _OutlineRole = _interopRequireDefault(require("./etc/objects/OutlineRole"));
var _ParagraphRole = _interopRequireDefault(require("./etc/objects/ParagraphRole"));
var _PopUpButtonRole = _interopRequireDefault(require("./etc/objects/PopUpButtonRole"));
var _PreRole = _interopRequireDefault(require("./etc/objects/PreRole"));
var _PresentationalRole = _interopRequireDefault(require("./etc/objects/PresentationalRole"));
var _ProgressIndicatorRole = _interopRequireDefault(require("./etc/objects/ProgressIndicatorRole"));
var _RadioButtonRole = _interopRequireDefault(require("./etc/objects/RadioButtonRole"));
var _RadioGroupRole = _interopRequireDefault(require("./etc/objects/RadioGroupRole"));
var _RegionRole = _interopRequireDefault(require("./etc/objects/RegionRole"));
var _RootWebAreaRole = _interopRequireDefault(require("./etc/objects/RootWebAreaRole"));
var _RowHeaderRole = _interopRequireDefault(require("./etc/objects/RowHeaderRole"));
var _RowRole = _interopRequireDefault(require("./etc/objects/RowRole"));
var _RubyRole = _interopRequireDefault(require("./etc/objects/RubyRole"));
var _RulerRole = _interopRequireDefault(require("./etc/objects/RulerRole"));
var _ScrollAreaRole = _interopRequireDefault(require("./etc/objects/ScrollAreaRole"));
var _ScrollBarRole = _interopRequireDefault(require("./etc/objects/ScrollBarRole"));
var _SeamlessWebAreaRole = _interopRequireDefault(require("./etc/objects/SeamlessWebAreaRole"));
var _SearchRole = _interopRequireDefault(require("./etc/objects/SearchRole"));
var _SearchBoxRole = _interopRequireDefault(require("./etc/objects/SearchBoxRole"));
var _SliderRole = _interopRequireDefault(require("./etc/objects/SliderRole"));
var _SliderThumbRole = _interopRequireDefault(require("./etc/objects/SliderThumbRole"));
var _SpinButtonRole = _interopRequireDefault(require("./etc/objects/SpinButtonRole"));
var _SpinButtonPartRole = _interopRequireDefault(require("./etc/objects/SpinButtonPartRole"));
var _SplitterRole = _interopRequireDefault(require("./etc/objects/SplitterRole"));
var _StaticTextRole = _interopRequireDefault(require("./etc/objects/StaticTextRole"));
var _StatusRole = _interopRequireDefault(require("./etc/objects/StatusRole"));
var _SVGRootRole = _interopRequireDefault(require("./etc/objects/SVGRootRole"));
var _SwitchRole = _interopRequireDefault(require("./etc/objects/SwitchRole"));
var _TabGroupRole = _interopRequireDefault(require("./etc/objects/TabGroupRole"));
var _TabRole = _interopRequireDefault(require("./etc/objects/TabRole"));
var _TableHeaderContainerRole = _interopRequireDefault(require("./etc/objects/TableHeaderContainerRole"));
var _TableRole = _interopRequireDefault(require("./etc/objects/TableRole"));
var _TabListRole = _interopRequireDefault(require("./etc/objects/TabListRole"));
var _TabPanelRole = _interopRequireDefault(require("./etc/objects/TabPanelRole"));
var _TermRole = _interopRequireDefault(require("./etc/objects/TermRole"));
var _TextAreaRole = _interopRequireDefault(require("./etc/objects/TextAreaRole"));
var _TextFieldRole = _interopRequireDefault(require("./etc/objects/TextFieldRole"));
var _TimeRole = _interopRequireDefault(require("./etc/objects/TimeRole"));
var _TimerRole = _interopRequireDefault(require("./etc/objects/TimerRole"));
var _ToggleButtonRole = _interopRequireDefault(require("./etc/objects/ToggleButtonRole"));
var _ToolbarRole = _interopRequireDefault(require("./etc/objects/ToolbarRole"));
var _TreeRole = _interopRequireDefault(require("./etc/objects/TreeRole"));
var _TreeGridRole = _interopRequireDefault(require("./etc/objects/TreeGridRole"));
var _TreeItemRole = _interopRequireDefault(require("./etc/objects/TreeItemRole"));
var _UserInterfaceTooltipRole = _interopRequireDefault(require("./etc/objects/UserInterfaceTooltipRole"));
var _VideoRole = _interopRequireDefault(require("./etc/objects/VideoRole"));
var _WebAreaRole = _interopRequireDefault(require("./etc/objects/WebAreaRole"));
var _WindowRole = _interopRequireDefault(require("./etc/objects/WindowRole"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
var AXObjects = [['AbbrRole', _AbbrRole.default], ['AlertDialogRole', _AlertDialogRole.default], ['AlertRole', _AlertRole.default], ['AnnotationRole', _AnnotationRole.default], ['ApplicationRole', _ApplicationRole.default], ['ArticleRole', _ArticleRole.default], ['AudioRole', _AudioRole.default], ['BannerRole', _BannerRole.default], ['BlockquoteRole', _BlockquoteRole.default], ['BusyIndicatorRole', _BusyIndicatorRole.default], ['ButtonRole', _ButtonRole.default], ['CanvasRole', _CanvasRole.default], ['CaptionRole', _CaptionRole.default], ['CellRole', _CellRole.default], ['CheckBoxRole', _CheckBoxRole.default], ['ColorWellRole', _ColorWellRole.default], ['ColumnHeaderRole', _ColumnHeaderRole.default], ['ColumnRole', _ColumnRole.default], ['ComboBoxRole', _ComboBoxRole.default], ['ComplementaryRole', _ComplementaryRole.default], ['ContentInfoRole', _ContentInfoRole.default], ['DateRole', _DateRole.default], ['DateTimeRole', _DateTimeRole.default], ['DefinitionRole', _DefinitionRole.default], ['DescriptionListDetailRole', _DescriptionListDetailRole.default], ['DescriptionListRole', _DescriptionListRole.default], ['DescriptionListTermRole', _DescriptionListTermRole.default], ['DetailsRole', _DetailsRole.default], ['DialogRole', _DialogRole.default], ['DirectoryRole', _DirectoryRole.default], ['DisclosureTriangleRole', _DisclosureTriangleRole.default], ['DivRole', _DivRole.default], ['DocumentRole', _DocumentRole.default], ['EmbeddedObjectRole', _EmbeddedObjectRole.default], ['FeedRole', _FeedRole.default], ['FigcaptionRole', _FigcaptionRole.default], ['FigureRole', _FigureRole.default], ['FooterRole', _FooterRole.default], ['FormRole', _FormRole.default], ['GridRole', _GridRole.default], ['GroupRole', _GroupRole.default], ['HeadingRole', _HeadingRole.default], ['IframePresentationalRole', _IframePresentationalRole.default], ['IframeRole', _IframeRole.default], ['IgnoredRole', _IgnoredRole.default], ['ImageMapLinkRole', _ImageMapLinkRole.default], ['ImageMapRole', _ImageMapRole.default], ['ImageRole', _ImageRole.default], ['InlineTextBoxRole', _InlineTextBoxRole.default], ['InputTimeRole', _InputTimeRole.default], ['LabelRole', _LabelRole.default], ['LegendRole', _LegendRole.default], ['LineBreakRole', _LineBreakRole.default], ['LinkRole', _LinkRole.default], ['ListBoxOptionRole', _ListBoxOptionRole.default], ['ListBoxRole', _ListBoxRole.default], ['ListItemRole', _ListItemRole.default], ['ListMarkerRole', _ListMarkerRole.default], ['ListRole', _ListRole.default], ['LogRole', _LogRole.default], ['MainRole', _MainRole.default], ['MarkRole', _MarkRole.default], ['MarqueeRole', _MarqueeRole.default], ['MathRole', _MathRole.default], ['MenuBarRole', _MenuBarRole.default], ['MenuButtonRole', _MenuButtonRole.default], ['MenuItemRole', _MenuItemRole.default], ['MenuItemCheckBoxRole', _MenuItemCheckBoxRole.default], ['MenuItemRadioRole', _MenuItemRadioRole.default], ['MenuListOptionRole', _MenuListOptionRole.default], ['MenuListPopupRole', _MenuListPopupRole.default], ['MenuRole', _MenuRole.default], ['MeterRole', _MeterRole.default], ['NavigationRole', _NavigationRole.default], ['NoneRole', _NoneRole.default], ['NoteRole', _NoteRole.default], ['OutlineRole', _OutlineRole.default], ['ParagraphRole', _ParagraphRole.default], ['PopUpButtonRole', _PopUpButtonRole.default], ['PreRole', _PreRole.default], ['PresentationalRole', _PresentationalRole.default], ['ProgressIndicatorRole', _ProgressIndicatorRole.default], ['RadioButtonRole', _RadioButtonRole.default], ['RadioGroupRole', _RadioGroupRole.default], ['RegionRole', _RegionRole.default], ['RootWebAreaRole', _RootWebAreaRole.default], ['RowHeaderRole', _RowHeaderRole.default], ['RowRole', _RowRole.default], ['RubyRole', _RubyRole.default], ['RulerRole', _RulerRole.default], ['ScrollAreaRole', _ScrollAreaRole.default], ['ScrollBarRole', _ScrollBarRole.default], ['SeamlessWebAreaRole', _SeamlessWebAreaRole.default], ['SearchRole', _SearchRole.default], ['SearchBoxRole', _SearchBoxRole.default], ['SliderRole', _SliderRole.default], ['SliderThumbRole', _SliderThumbRole.default], ['SpinButtonRole', _SpinButtonRole.default], ['SpinButtonPartRole', _SpinButtonPartRole.default], ['SplitterRole', _SplitterRole.default], ['StaticTextRole', _StaticTextRole.default], ['StatusRole', _StatusRole.default], ['SVGRootRole', _SVGRootRole.default], ['SwitchRole', _SwitchRole.default], ['TabGroupRole', _TabGroupRole.default], ['TabRole', _TabRole.default], ['TableHeaderContainerRole', _TableHeaderContainerRole.default], ['TableRole', _TableRole.default], ['TabListRole', _TabListRole.default], ['TabPanelRole', _TabPanelRole.default], ['TermRole', _TermRole.default], ['TextAreaRole', _TextAreaRole.default], ['TextFieldRole', _TextFieldRole.default], ['TimeRole', _TimeRole.default], ['TimerRole', _TimerRole.default], ['ToggleButtonRole', _ToggleButtonRole.default], ['ToolbarRole', _ToolbarRole.default], ['TreeRole', _TreeRole.default], ['TreeGridRole', _TreeGridRole.default], ['TreeItemRole', _TreeItemRole.default], ['UserInterfaceTooltipRole', _UserInterfaceTooltipRole.default], ['VideoRole', _VideoRole.default], ['WebAreaRole', _WebAreaRole.default], ['WindowRole', _WindowRole.default]];
var AXObjectsMap = {
  entries: function entries() {
    return AXObjects;
  },
  forEach: function forEach(fn) {
    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    for (var _i = 0, _AXObjects = AXObjects; _i < _AXObjects.length; _i++) {
      var _AXObjects$_i = _slicedToArray(_AXObjects[_i], 2),
        key = _AXObjects$_i[0],
        values = _AXObjects$_i[1];
      fn.call(thisArg, values, key, AXObjects);
    }
  },
  get: function get(key) {
    var item = AXObjects.find(function (tuple) {
      return tuple[0] === key ? true : false;
    });
    return item && item[1];
  },
  has: function has(key) {
    return !!AXObjectsMap.get(key);
  },
  keys: function keys() {
    return AXObjects.map(function (_ref) {
      var _ref2 = _slicedToArray(_ref, 1),
        key = _ref2[0];
      return key;
    });
  },
  values: function values() {
    return AXObjects.map(function (_ref3) {
      var _ref4 = _slicedToArray(_ref3, 2),
        values = _ref4[1];
      return values;
    });
  }
};
var _default = (0, _iterationDecorator.default)(AXObjectsMap, AXObjectsMap.entries());
exports.default = _default;