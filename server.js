const express = require('express');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// 中间件
app.use(cors());
app.use(express.json());

// OpenWeatherMap API配置
const WEATHER_API_KEY = process.env.WEATHER_API_KEY || 'demo_key';
const WEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5';

// 获取当前天气
app.get('/api/weather/current/:city', async (req, res) => {
  try {
    const { city } = req.params;
    const response = await axios.get(
      `${WEATHER_BASE_URL}/weather?q=${city}&appid=${WEATHER_API_KEY}&units=metric&lang=zh_cn`
    );
    
    const weatherData = {
      city: response.data.name,
      country: response.data.sys.country,
      temperature: Math.round(response.data.main.temp),
      description: response.data.weather[0].description,
      humidity: response.data.main.humidity,
      windSpeed: response.data.wind.speed,
      windDirection: response.data.wind.deg,
      pressure: response.data.main.pressure,
      visibility: response.data.visibility / 1000, // 转换为公里
      icon: response.data.weather[0].icon,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: weatherData
    });
  } catch (error) {
    console.error('获取天气数据失败:', error.message);
    res.status(500).json({
      success: false,
      message: '获取天气数据失败',
      error: error.response?.data?.message || error.message
    });
  }
});

// 获取5天天气预报
app.get('/api/weather/forecast/:city', async (req, res) => {
  try {
    const { city } = req.params;
    const response = await axios.get(
      `${WEATHER_BASE_URL}/forecast?q=${city}&appid=${WEATHER_API_KEY}&units=metric&lang=zh_cn`
    );
    
    const forecastData = response.data.list.map(item => ({
      datetime: item.dt_txt,
      temperature: Math.round(item.main.temp),
      description: item.weather[0].description,
      humidity: item.main.humidity,
      windSpeed: item.wind.speed,
      icon: item.weather[0].icon
    }));

    res.json({
      success: true,
      data: {
        city: response.data.city.name,
        country: response.data.city.country,
        forecast: forecastData
      }
    });
  } catch (error) {
    console.error('获取天气预报失败:', error.message);
    res.status(500).json({
      success: false,
      message: '获取天气预报失败',
      error: error.response?.data?.message || error.message
    });
  }
});

// 无人机飞行适宜性评估
app.get('/api/drone/flight-conditions/:city', async (req, res) => {
  try {
    const { city } = req.params;
    const response = await axios.get(
      `${WEATHER_BASE_URL}/weather?q=${city}&appid=${WEATHER_API_KEY}&units=metric&lang=zh_cn`
    );
    
    const weather = response.data;
    const windSpeed = weather.wind.speed;
    const visibility = weather.visibility / 1000;
    const precipitation = weather.rain?.['1h'] || weather.snow?.['1h'] || 0;
    
    // 飞行条件评估逻辑
    let flightSuitability = 'excellent';
    let warnings = [];
    
    if (windSpeed > 10) {
      flightSuitability = 'poor';
      warnings.push('风速过大，不适宜飞行');
    } else if (windSpeed > 7) {
      flightSuitability = 'fair';
      warnings.push('风速较大，需谨慎飞行');
    }
    
    if (visibility < 5) {
      flightSuitability = 'poor';
      warnings.push('能见度低，不适宜飞行');
    } else if (visibility < 10) {
      if (flightSuitability === 'excellent') flightSuitability = 'fair';
      warnings.push('能见度一般，需注意观察');
    }
    
    if (precipitation > 0) {
      flightSuitability = 'poor';
      warnings.push('有降水，不适宜飞行');
    }

    res.json({
      success: true,
      data: {
        city: weather.name,
        flightSuitability,
        warnings,
        conditions: {
          windSpeed,
          visibility,
          precipitation,
          temperature: Math.round(weather.main.temp),
          humidity: weather.main.humidity,
          description: weather.weather[0].description
        },
        recommendation: flightSuitability === 'excellent' ? '适宜飞行' : 
                       flightSuitability === 'fair' ? '谨慎飞行' : '不建议飞行'
      }
    });
  } catch (error) {
    console.error('获取飞行条件失败:', error.message);
    res.status(500).json({
      success: false,
      message: '获取飞行条件失败',
      error: error.response?.data?.message || error.message
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '气象API服务运行正常',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`API文档: http://localhost:${PORT}/api/health`);
});
