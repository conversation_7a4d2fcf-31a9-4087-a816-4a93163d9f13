# 集成气象API的无人机租赁系统演示

这是一个集成气象API的演示项目，展示了如何在无人机租赁系统中集成实时天气数据，为用户提供飞行条件评估。

## 功能特性

- 🌤️ **实时天气查询** - 获取指定城市的当前天气信息
- 📅 **天气预报** - 显示5天天气预报数据
- 🚁 **飞行条件评估** - 基于天气数据评估无人机飞行适宜性
- 🎨 **现代化UI** - 响应式设计，支持移动端
- 🔄 **实时数据** - 集成OpenWeatherMap API获取准确天气数据

## 技术栈

### 后端
- Node.js + Express
- Axios (HTTP客户端)
- CORS (跨域支持)
- dotenv (环境变量管理)

### 前端
- React 18
- Axios (API调用)
- CSS3 (现代化样式)

## 快速开始

### 1. 获取API密钥

1. 访问 [OpenWeatherMap](https://openweathermap.org/api)
2. 注册免费账户
3. 获取API密钥

### 2. 安装依赖

```bash
# 安装后端依赖
npm install

# 安装前端依赖
npm run install-client
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
copy .env.example .env

# 编辑 .env 文件，添加你的API密钥
WEATHER_API_KEY=your_api_key_here
PORT=5000
```

### 4. 启动应用

```bash
# 启动后端服务器
npm start

# 在新的终端窗口启动前端
npm run client
```

应用将在以下地址运行：
- 前端: http://localhost:3000
- 后端API: http://localhost:5000

## API接口

### 获取当前天气
```
GET /api/weather/current/:city
```

### 获取天气预报
```
GET /api/weather/forecast/:city
```

### 获取飞行条件评估
```
GET /api/drone/flight-conditions/:city
```

### 健康检查
```
GET /api/health
```

## 飞行条件评估规则

系统根据以下气象参数评估无人机飞行适宜性：

- **风速**
  - ≤ 7 m/s: 适宜飞行
  - 7-10 m/s: 谨慎飞行
  - > 10 m/s: 不适宜飞行

- **能见度**
  - ≥ 10 km: 适宜飞行
  - 5-10 km: 谨慎飞行
  - < 5 km: 不适宜飞行

- **降水**
  - 无降水: 适宜飞行
  - 有降水: 不适宜飞行

## 项目结构

```
├── server.js              # 后端服务器
├── package.json           # 后端依赖配置
├── .env.example          # 环境变量模板
├── client/               # 前端应用
│   ├── public/
│   ├── src/
│   │   ├── App.js        # 主应用组件
│   │   ├── index.js      # 应用入口
│   │   └── index.css     # 样式文件
│   └── package.json      # 前端依赖配置
└── README.md             # 项目说明
```

## 开发说明

### 添加新的气象数据源

1. 在 `server.js` 中添加新的API端点
2. 实现数据获取和处理逻辑
3. 在前端 `App.js` 中添加对应的UI组件

### 自定义飞行条件评估

修改 `server.js` 中的 `/api/drone/flight-conditions/:city` 端点，调整评估规则和阈值。

## 注意事项

- 免费的OpenWeatherMap API有调用次数限制
- 建议在生产环境中添加API调用缓存
- 可以根据实际需求调整飞行条件评估规则

## 许可证

MIT License
