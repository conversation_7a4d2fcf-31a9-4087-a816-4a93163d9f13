import React, { useState } from 'react';
import axios from 'axios';
import './index.css';

function App() {
  const [city, setCity] = useState('北京');
  const [activeTab, setActiveTab] = useState('current');
  const [weatherData, setWeatherData] = useState(null);
  const [forecastData, setForecastData] = useState(null);
  const [flightData, setFlightData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = async () => {
    if (!city.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      if (activeTab === 'current') {
        const response = await axios.get(`/api/weather/current/${encodeURIComponent(city)}`);
        setWeatherData(response.data.data);
      } else if (activeTab === 'forecast') {
        const response = await axios.get(`/api/weather/forecast/${encodeURIComponent(city)}`);
        setForecastData(response.data.data);
      } else if (activeTab === 'flight') {
        const response = await axios.get(`/api/drone/flight-conditions/${encodeURIComponent(city)}`);
        setFlightData(response.data.data);
      }
    } catch (err) {
      setError(err.response?.data?.message || '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setWeatherData(null);
    setForecastData(null);
    setFlightData(null);
    setError(null);
  };

  const getWeatherIconUrl = (icon) => {
    return `https://openweathermap.org/img/wn/${icon}@2x.png`;
  };

  return (
    <div className="container">
      <div className="header">
        <h1>🌤️ 气象API集成演示</h1>
        <p>集成气象数据的无人机租赁系统</p>
      </div>

      <div className="search-section">
        <div className="search-form">
          <input
            type="text"
            className="search-input"
            placeholder="请输入城市名称（如：北京、上海、广州）"
            value={city}
            onChange={(e) => setCity(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button 
            className="search-btn" 
            onClick={handleSearch}
            disabled={loading}
          >
            {loading ? '查询中...' : '查询天气'}
          </button>
        </div>

        <div className="tabs">
          <button 
            className={`tab ${activeTab === 'current' ? 'active' : ''}`}
            onClick={() => handleTabChange('current')}
          >
            当前天气
          </button>
          <button 
            className={`tab ${activeTab === 'forecast' ? 'active' : ''}`}
            onClick={() => handleTabChange('forecast')}
          >
            天气预报
          </button>
          <button 
            className={`tab ${activeTab === 'flight' ? 'active' : ''}`}
            onClick={() => handleTabChange('flight')}
          >
            飞行条件
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      {loading && (
        <div className="loading">
          🔄 正在获取数据...
        </div>
      )}

      {/* 当前天气 */}
      {activeTab === 'current' && weatherData && (
        <div className="weather-card">
          <div className="weather-header">
            <h2 className="weather-title">
              📍 {weatherData.city}, {weatherData.country}
            </h2>
            <img 
              src={getWeatherIconUrl(weatherData.icon)} 
              alt={weatherData.description}
              className="weather-icon"
            />
          </div>
          
          <div className="weather-main">
            <div className="weather-item">
              <div className="weather-value">{weatherData.temperature}°C</div>
              <div className="weather-label">温度</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{weatherData.humidity}%</div>
              <div className="weather-label">湿度</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{weatherData.windSpeed} m/s</div>
              <div className="weather-label">风速</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{weatherData.visibility} km</div>
              <div className="weather-label">能见度</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{weatherData.pressure} hPa</div>
              <div className="weather-label">气压</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{weatherData.description}</div>
              <div className="weather-label">天气状况</div>
            </div>
          </div>
        </div>
      )}

      {/* 天气预报 */}
      {activeTab === 'forecast' && forecastData && (
        <div className="weather-card">
          <div className="weather-header">
            <h2 className="weather-title">
              📅 {forecastData.city} 5天天气预报
            </h2>
          </div>

          <div className="forecast-grid">
            {forecastData.forecast.slice(0, 8).map((item, index) => (
              <div key={index} className="forecast-item">
                <div className="forecast-time">
                  {new Date(item.datetime).toLocaleDateString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit'
                  })}
                </div>
                <img
                  src={getWeatherIconUrl(item.icon)}
                  alt={item.description}
                  style={{width: '40px', height: '40px'}}
                />
                <div className="forecast-temp">{item.temperature}°C</div>
                <div className="forecast-desc">{item.description}</div>
                <div style={{fontSize: '0.8rem', color: '#999', marginTop: '5px'}}>
                  💨 {item.windSpeed} m/s
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 飞行条件 */}
      {activeTab === 'flight' && flightData && (
        <div className="flight-conditions">
          <div className="weather-header">
            <h2 className="weather-title">
              🚁 {flightData.city} 无人机飞行条件评估
            </h2>
          </div>

          <div className="flight-status">
            <div className={`status-indicator status-${flightData.flightSuitability}`}></div>
            <div className="status-text">
              飞行适宜性: {flightData.recommendation}
            </div>
          </div>

          <div className="weather-main">
            <div className="weather-item">
              <div className="weather-value">{flightData.conditions.windSpeed} m/s</div>
              <div className="weather-label">风速</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{flightData.conditions.visibility} km</div>
              <div className="weather-label">能见度</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{flightData.conditions.temperature}°C</div>
              <div className="weather-label">温度</div>
            </div>
            <div className="weather-item">
              <div className="weather-value">{flightData.conditions.humidity}%</div>
              <div className="weather-label">湿度</div>
            </div>
          </div>

          {flightData.warnings.length > 0 && (
            <div className="warnings">
              <h3>⚠️ 注意事项:</h3>
              {flightData.warnings.map((warning, index) => (
                <div key={index} className="warning-item">
                  {warning}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default App;
