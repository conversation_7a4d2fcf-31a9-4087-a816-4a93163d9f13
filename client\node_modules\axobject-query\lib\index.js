"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.elementAXObjects = exports.AXObjects = exports.AXObjectRoles = exports.AXObjectElements = void 0;
var _AXObjectElementMap = _interopRequireDefault(require("./AXObjectElementMap"));
var _AXObjectRoleMap = _interopRequireDefault(require("./AXObjectRoleMap"));
var _AXObjectsMap = _interopRequireDefault(require("./AXObjectsMap"));
var _elementAXObjectMap = _interopRequireDefault(require("./elementAXObjectMap"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var AXObjectElements = _AXObjectElementMap.default;
exports.AXObjectElements = AXObjectElements;
var AXObjectRoles = _AXObjectRoleMap.default;
exports.AXObjectRoles = AXObjectRoles;
var AXObjects = _AXObjectsMap.default;
exports.AXObjects = AXObjects;
var elementAXObjects = _elementAXObjectMap.default;
exports.elementAXObjects = elementAXObjects;