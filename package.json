{"name": "weather-api-demo", "version": "1.0.0", "description": "集成气象API的无人机租赁系统演示", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm start", "build": "cd client && npm run build", "install-client": "cd client && npm install"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["weather", "api", "drone", "rental"], "author": "", "license": "MIT"}